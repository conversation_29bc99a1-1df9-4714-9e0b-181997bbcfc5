/*

TemplateMo 584 Pod Talk

https://templatemo.com/tm-584-pod-talk

*/



/*---------------------------------------
  CUSTOM PROPERTIES ( VARIABLES )             
-----------------------------------------*/
:root {
  --white-color:                  #ffffff;
  --primary-color:                #00CC99;
  --secondary-color:              #0066CC;
  --section-bg-color:             #f0f8ff;
  --custom-btn-bg-color:          #0066CC;
  --custom-btn-bg-hover-color:    #00CC99;
  --dark-color:                   #000000;
  --p-color:                      #717275;
  --border-color:                 #7fffd4;
  --link-hover-color:             #0066CC;

  --body-font-family:             'Montserrat', sans-serif;
  --title-font-family:            'Sono', sans-serif;

  --h1-font-size:                 58px;
  --h2-font-size:                 46px;
  --h3-font-size:                 32px;
  --h4-font-size:                 28px;
  --h5-font-size:                 24px;
  --h6-font-size:                 22px;
  --p-font-size:                  16px;
  --menu-font-size:               14px;

  --border-radius-large:          100px;
  --border-radius-medium:         20px;
  --border-radius-small:          10px;

  --font-weight-light:            300;
  --font-weight-normal:           400;
  --font-weight-medium:           500;
  --font-weight-semibold:         600;
  --font-weight-bold:             700;
}

body {
  background-color: var(--white-color);
  font-family: var(--body-font-family); 
}


/*---------------------------------------
  TYPOGRAPHY               
-----------------------------------------*/

h2,
h3,
h4,
h5,
h6 {
  color: var(--dark-color);
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: var(--title-font-family); 
  font-weight: var(--font-weight-semibold);
}

h1 {
  font-size: var(--h1-font-size);
  font-weight: var(--font-weight-bold);
}

h2 {
  font-size: var(--h2-font-size);
  font-weight: var(--font-weight-bold);
}

h3 {
  font-size: var(--h3-font-size);
}

h4 {
  font-size: var(--h4-font-size);
}

h5 {
  font-size: var(--h5-font-size);
}

h6 {
  font-size: var(--h6-font-size);
}

p {
  color: var(--p-color);
  font-size: var(--p-font-size);
  font-weight: var(--font-weight-light);
}

ul li {
  color: var(--p-color);
  font-size: var(--p-font-size);
  font-weight: var(--font-weight-light);
}

a, 
button {
  touch-action: manipulation;
  transition: all 0.3s;
}

a {
  display: inline-block;
  color: var(--primary-color);
  text-decoration: none;
}

a:hover {
  color: var(--link-hover-color);
}

b,
strong {
  font-weight: var(--font-weight-bold);
}

::selection {
  background-color: var(--primary-color);
  color: var(--white-color);
}


/*---------------------------------------
  SECTION               
-----------------------------------------*/
.section-title-wrap {
  position: relative;
}

.section-title-wrap::after {
  content: "";
  background: var(--section-bg-color);
  width: 100%;
  height: 2px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  pointer-events: none;
}

.section-title {
  background: var(--section-bg-color);
  border-radius: var(--border-radius-large);
  display: inline-block;
  position: relative;
  z-index: 2;
  margin-bottom: 0;
  padding: 10px 25px;
}

.section-padding {
  padding-top: 100px;
  padding-bottom: 100px;
}

main {
  position: relative;
  z-index: 1;
}

.section-bg {
  background-color: var(--section-bg-color);
}

.section-overlay {
  background-color: var(--primary-color);
  position: absolute;
  top: 0;
  left: 0;
  pointer-events: none;
  width: 100%;
  height: 100%;
  opacity: 0.85;
}

.section-overlay + .container {
  position: relative;
}


/*---------------------------------------
  CUSTOM ICON COLOR               
-----------------------------------------*/
.custom-icon {
  color: var(--primary-color);
}


/*---------------------------------------
  CUSTOM BUTTON               
-----------------------------------------*/
.custom-btn {
  background: var(--custom-btn-bg-color);
  border: 2px solid transparent;
  border-radius: var(--border-radius-large);
  color: var(--white-color);
  font-family: var(--title-font-family);
  font-size: var(--p-font-size);
  font-weight: var(--font-weight-semibold);
  line-height: normal;
  transition: all 0.3s;
  padding: 10px 20px;
}

.custom-btn:hover {
  background: var(--custom-btn-bg-hover-color);
  color: var(--white-color);
}

.custom-border-btn {
  background: transparent;
  border: 2px solid var(--custom-btn-bg-color);
  color: var(--custom-btn-bg-color);
}

.custom-border-btn:hover {
  background: var(--custom-btn-bg-color);
  border-color: transparent;
  color: var(--primary-color);
}

.custom-btn-bg-white {
  border-color: var(--white-color);
  color: var(--white-color);
}


/*---------------------------------------
  SITE HEADER              
-----------------------------------------*/
.site-header {
  background-image: url('../images/templatemo-wave-header.jpg'), linear-gradient(#348CD2, #FFFFFF);;
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
  min-height: 480px;
  position: relative;
}

.site-header h2 {
  color: var(--white-color);
}


/*---------------------------------------
  NAVIGATION              
-----------------------------------------*/
.navbar {
  background-color: transparent;
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  z-index: 9;
  padding-top: 20px;
  padding-bottom: 20px;
}

.navbar .navbar-brand,
.navbar .navbar-brand:hover {
  color: var(--white-color);
}

.navbar .logo-image {
  width: 100px;
}

.logo-image {
  width: 120px;
  height: auto;
}

.navbar-brand,
.navbar-brand:hover {
  font-size: var(--h3-font-size);
  font-weight: var(--font-weight-bold);
  display: inline-block;
}

.navbar-brand span {
  font-family: var(--title-font-family);
}

.navbar-nav .nav-link {
  display: inline-block;
  color: var(--section-bg-color);
  font-family: var(--title-font-family); 
  font-size: var(--menu-font-size);
  font-weight: var(--font-weight-medium);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  position: relative;
  padding-top: 15px;
  padding-bottom: 15px;
}

.navbar-expand-lg .navbar-nav .nav-link {
  padding-right: 18px;
  padding-left: 18px;
}

.navbar-nav .nav-link.active, 
.navbar-nav .nav-link:hover {
  color: var(--secondary-color);
}

.navbar .dropdown-menu {
  background: var(--white-color);
  box-shadow: 0 1rem 3rem rgba(0,0,0,.175);
  border: 0;
  display: inherit;
  opacity: 0;
  min-width: 9rem;
  margin-top: 20px;
  padding: 13px 0 10px 0;
  transition: all 0.3s;
  pointer-events: none;
}

.navbar .dropdown-menu::before {
  content: "";
  width: 0;
  height: 0;
  border-left: 20px solid transparent;
  border-right: 20px solid transparent;
  border-bottom: 15px solid var(--white-color);
  position: absolute;
  top: -10px;
  left: 10px;
}

.navbar .dropdown-item {
  display: inline-block;
  color: var(--p-bg-color);
  font-family: var(--title-font-family); 
  font-size: var(--menu-font-size);
  font-weight: var(--font-weight-medium);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  position: relative;
}

.navbar .dropdown-item.active, 
.navbar .dropdown-item:active,
.navbar .dropdown-item:focus, 
.navbar .dropdown-item:hover {
  background: transparent;
  color: var(--secondary-color);
}

.navbar .dropdown-toggle::after {
  content: "\f282";
  display: inline-block;
  font-family: bootstrap-icons !important;
  font-size: var(--menu-font-size);
  font-style: normal;
  font-weight: normal !important;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  vertical-align: -.125em;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  position: relative;
  left: 2px;
  border: 0;
}

@media screen and (min-width: 992px) {
  .navbar .dropdown:hover .dropdown-menu {
    opacity: 1;
    margin-top: 0;
    pointer-events: auto;
  }
}

.navbar .custom-border-btn {
  border-color: var(--white-color);
  color: var(--white-color);
}

.navbar .custom-border-btn:hover {
  background: var(--white-color);
  color: var(--secondary-color);
}

.navbar-toggler {
  border: 0;
  padding: 0;
  cursor: pointer;
  margin: 0;
  width: 30px;
  height: 35px;
  outline: none;
}

.navbar-toggler:focus {
  outline: none;
  box-shadow: none;
}

.navbar-toggler[aria-expanded="true"] .navbar-toggler-icon {
  background: transparent;
}

.navbar-toggler[aria-expanded="true"] .navbar-toggler-icon:before,
.navbar-toggler[aria-expanded="true"] .navbar-toggler-icon:after {
  transition: top 300ms 50ms ease, -webkit-transform 300ms 350ms ease;
  transition: top 300ms 50ms ease, transform 300ms 350ms ease;
  transition: top 300ms 50ms ease, transform 300ms 350ms ease, -webkit-transform 300ms 350ms ease;
  top: 0;
}

.navbar-toggler[aria-expanded="true"] .navbar-toggler-icon:before {
  transform: rotate(45deg);
}

.navbar-toggler[aria-expanded="true"] .navbar-toggler-icon:after {
  transform: rotate(-45deg);
}

.navbar-toggler .navbar-toggler-icon {
  background: var(--white-color);
  transition: background 10ms 300ms ease;
  display: block;
  width: 30px;
  height: 2px;
  position: relative;
}

.navbar-toggler .navbar-toggler-icon:before,
.navbar-toggler .navbar-toggler-icon:after {
  transition: top 300ms 350ms ease, -webkit-transform 300ms 50ms ease;
  transition: top 300ms 350ms ease, transform 300ms 50ms ease;
  transition: top 300ms 350ms ease, transform 300ms 50ms ease, -webkit-transform 300ms 50ms ease;
  position: absolute;
  right: 0;
  left: 0;
  background: var(--white-color);
  width: 30px;
  height: 2px;
  content: '';
}

.navbar-toggler .navbar-toggler-icon::before {
  top: -8px;
}

.navbar-toggler .navbar-toggler-icon::after {
  top: 8px;
}


/*---------------------------------------
  CAROUSEL        
-----------------------------------------*/
.hero-section {
  background-image: url('../images/templatemo-wave-banner.jpg'), linear-gradient(#348CD2, #FFFFFF);
  background-repeat: no-repeat;
  background-size: 108% 76%;
  background-position: top;
  padding-top: 200px;
}

.owl-carousel {
  text-align: center;
}

.owl-carousel-image {
  display: block;
}

.owl-carousel .owl-item .owl-carousel-verified-image {
  display: inline-block;
  width: 30px;
  height: auto;
  position: relative;
  right: 5px;
}

.verified-image {
  display: inline-block;
  width: 20px;
  height: auto;
}

.owl-carousel .owl-item {
  opacity: 0.35;
}

.owl-carousel .owl-item.active.center {
  opacity: 1;
}

.owl-carousel-info-wrap {
  border-radius: var(--border-radius-medium);
  position: relative;
  overflow: hidden;
  text-align: left;
}

.owl-carousel-info {
  background-color: var(--section-bg-color);
  box-shadow: 0 1rem 3rem rgba(0,0,0,.175);
  position: absolute;
  bottom: 0;
  right: 0;
  left: 0;
  padding: 30px;
}

.badge {
  background-color: var(--custom-btn-bg-color);
  font-family: var(--title-font-family);
  border-radius: var(--border-radius-large);
  color: var(--white-color);
  padding-bottom: 5px;
}

.owl-carousel-info-wrap .social-share,
.team-thumb .social-share {
  position: absolute;
  right: 0;
  bottom: 0;
}

.owl-carousel-info-wrap .social-icon,
.team-thumb .social-icon {
  opacity: 0;
  transition: all 0.3s ease;
  transform: translateX(0);
  padding-right: 20px;
  padding-left: 20px;
}

.owl-carousel .owl-item.active.center .owl-carousel-info-wrap:hover .social-icon,
.team-thumb:hover .social-icon {
  transform: translateY(-100%);
  opacity: 1;
}

.owl-carousel-info-wrap .social-icon-item,
.owl-carousel-info-wrap .social-icon-link,
.team-thumb .social-icon-item,
.team-thumb .social-icon-link {
  display: block;
  margin-bottom: 10px;
  margin-left: auto;
}

.owl-carousel-info-wrap .social-icon-link {
  margin-top: 5px;
  margin-bottom: 5px;
}

.owl-carousel .owl-dots {
  background-color: var(--white-color);
  box-shadow: 0 1rem 3rem rgba(0,0,0,.175);
  border-radius: var(--border-radius-large);
  display: inline-block;
  margin: auto;
  margin-top: 40px;
  padding: 15px 25px;
  padding-bottom: 7px;
}

.owl-theme .owl-nav.disabled+.owl-dots {
  margin-top: 40px;
}

.owl-theme .owl-dots .owl-dot.active span, 
.owl-theme .owl-dots .owl-dot:hover span {
  background: var(--secondary-color);
}


/*---------------------------------------
  CUSTOM BLOCK              
-----------------------------------------*/
.custom-block {
  border: 2px solid var(--primary-color);
  border-radius: var(--border-radius-medium);
  position: relative;
  overflow: hidden;
  padding: 30px;
  transition: all 0.3s ease;
}

.custom-block:hover {
  background: var(--white-color);
  box-shadow: 0 1rem 3rem rgba(0,0,0,.175);
  border-color: transparent;
  transform: translateY(-3px);
}

.custom-block-info {
  display: block;
  padding: 10px 20px;
  padding-bottom: 0;
}

.custom-block-image-wrap {
  position: relative;
  display: block;
  height: 100%;
}

.custom-block-image-wrap > a {
  display: block;
}

.custom-block-image {
  border-radius: var(--border-radius-medium);
  display: block;
  width: 112px;
  height: 112px;
  object-fit: cover;
}

.custom-block-image-detail-page .custom-block-image {
  width: 100%;
  height: 212px;
}

.custom-block .custom-block-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.custom-block-icon-wrap {
  border-radius: var(--border-radius-medium);
  position: relative;
  overflow: hidden;
}

.custom-block-icon-wrap .section-overlay {
  opacity: 0.25;
}

.custom-block-btn-group {
  position: absolute;
  bottom: 0;
  right: 0;
  left: 0;
  margin: 20px;
}

.custom-block-btn-group .custom-block-icon {
  position: relative;
  top: 0;
  left: 0;
  transform: none;
}

.custom-block-icon {
  background: var(--primary-color);
  border-radius: var(--border-radius-medium);
  font-size: var(--p-font-size);
  color: var(--white-color);
  text-align: center;
  width: 32.5px;
  height: 32.5px;
  line-height: 32.5px;
  transition: all 0.3s;
}

.custom-block-icon:hover {
  background: var(--secondary-color);
  color: var(--white-color);
}

.custom-block .custom-btn {
  font-size: var(--menu-font-size);
  padding: 7px 15px;
}

.custom-block .custom-block-info + div .badge {
  background-color: var(--dark-color);
  color: var(--white-color);
  border-radius: 50px !important;
  font-size: var(--menu-font-size);
  display: flex;
  justify-content: center;
  text-align: center;
  width: 40px;
  height: 40px;
  line-height: 30px;
  margin-top: 5px;
  margin-bottom: 5px;
}

.custom-block .custom-block-info + div .badge:hover {
  background-color: var(--secondary-color);
}

.custom-block-full {
  background-color: var(--section-bg-color);
  border-color: transparent;
}

.custom-block-full:hover {
  border-color: var(--primary-color);
}

.custom-block-full .custom-block-info {
  padding: 20px;
  padding-bottom: 0;
}

.custom-block-full .custom-block-image {
  width: 100%;
  height: 210px;
}

.custom-block-full .social-share {
  position: absolute;
  top: 0;
  right: 0;
  margin: 50px;
}

.custom-block-top small {
  color: var(--p-color);
  font-family: var(--title-font-family);
}

.custom-block-top .badge {
  background-color: var(--secondary-color);
  color: var(--white-color);
  display: inline-block;
  vertical-align: middle;
  height: 26.64px;
  line-height: 20px;
}

.custom-block-bottom a:hover span {
  color: var(--primary-color);
}

.custom-block-bottom a span {
  font-family: var(--title-font-family);
  color: var(--p-color);
  text-transform: uppercase;
  margin-left: 3px;
}

.custom-block-overlay {
  border-color: transparent;
  padding: 0;
}

.custom-block-overlay .custom-block-image {
  margin: auto;
  width: 100%;
  height: 210px;
  transition: all 0.3s;
}

.custom-block-overlay:hover .custom-block-image {
  padding: 15px;
  padding-bottom: 0;
}

.custom-block-overlay-info {
  padding: 15px 20px 20px 20px;
}


/*---------------------------------------
  PROIFLE BLOCK               
-----------------------------------------*/
.profile-block {
  margin-top: 10px;
}

.profile-block-image {
  border-radius: var(--border-radius-large);
  width: 50px;
  height: 50px;
  object-fit: cover;
  margin-right: 10px;
}

.profile-block p strong {
  display: block;
  font-family: var(--title-font-family);
}

.profile-detail-block {
  border: 1px solid #dee2e6;
  border-radius: var(--border-radius-large);
  padding: 25px 35px;
}

.profile-detail-block p {
  margin-bottom: 0;
}


/*---------------------------------------
  ABOUT & TEAM SECTION               
-----------------------------------------*/
.about-image {
  border-radius: var(--border-radius-medium);
  display: block;
}

.team-thumb {
  border-radius: var(--border-radius-medium);
  position: relative;
  overflow: hidden;
}

.team-info {
  background-color: var(--white-color);
  position: absolute;
  bottom: 0;
  right: 0;
  left: 0;
  padding: 30px;
}


/*---------------------------------------
  PAGINATION               
-----------------------------------------*/
.pagination {
  border: 1px solid #dee2e6;
  border-radius: var(--border-radius-large);
  padding: 20px;
}

.page-link {
  border: 0;
  border-radius: var(--border-radius-small);
  color: var(--p-color);
  font-family: var(--title-font-family);
  margin: 0 5px;
  padding: 10px 20px;
}

.page-link:hover,
.page-item:first-child .page-link:hover,
.page-item:last-child .page-link:hover {
  background-color: var(--secondary-color);
  color: var(--white-color);
}

.page-item:first-child .page-link {
  margin-right: 10px;
}

.active>.page-link, .page-link.active {
  background-color: var(--secondary-color);
  border-color: var(--secondary-color);
}


/*---------------------------------------
  CONTACT               
-----------------------------------------*/
.contact-info p strong {
  font-family: var(--title-font-family);
  min-width: 90px;
}

.contact-info p a {
  color: var(--p-color);
  border-bottom: 1px solid;
  padding-bottom: 3px;
}

.contact-info p a:hover {
  color: var(--secondary-color);
}

.google-map {
  border-radius: var(--border-radius-medium);
}

.contact-form .form-floating>textarea {
  border-radius: var(--border-radius-medium);
  height: 150px;
}


/*---------------------------------------
  SUBSCRIBE FORM               
-----------------------------------------*/
.subscribe-form-wrap {
  border: 1px solid var(--white-color);
  border-radius: var(--border-radius-small);
  width: 80%;
  position: relative;
  top: 12px;
  padding: 35px;
}

.subscribe-form-wrap h6 {
  background: var(--white-color);
  border-radius: var(--border-radius-medium);
  color: var(--primary-color);
  text-align: center;
  position: relative;
  bottom: 55px;
  margin-bottom: -25px;
  padding: 8px;
}

.subscribe-form #subscribe-email {
  border: 0;
  border-radius: 10px 10px 0 0;
  margin-bottom: 0;
}

.subscribe-form #submit {
  border-radius: 0 0 10px 10px;
}


/*---------------------------------------
  CUSTOM FORM               
-----------------------------------------*/
.custom-form .form-control {
  border-radius: var(--border-radius-small);
  color: var(--p-color);
  font-family: var(--title-font-family);
  font-size: var(--p-font-size);
  margin-bottom: 24px;
  padding-top: 10px;
  padding-bottom: 10px;
  padding-left: 20px;
  outline: none;
}

.form-floating>label {
  padding-left: 20px;
}

.custom-form button[type="submit"] {
  background: var(--custom-btn-bg-color);
  border: none;
  border-radius: var(--border-radius-large);
  color: var(--white-color);
  font-family: var(--title-font-family);
  font-size: var(--p-font-size);
  font-weight: var(--font-weight-semibold);
  transition: all 0.3s;
  margin-bottom: 0;
}

.custom-form button[type="submit"]:hover,
.custom-form button[type="submit"]:focus {
  background: var(--custom-btn-bg-hover-color);
  border-color: transparent;
}


/*---------------------------------------
  SEARCH FORM               
-----------------------------------------*/
.search-form .form-control {
  border: 0;
  margin-bottom: 0;
}

.search-form button[type="submit"] {
  background: var(--secondary-color);
  border-color: var(--secondary-color);
  max-width: 50px;
  height: 100%;
  padding-left: 15px;
}

.search-form button[type="submit"]:hover {
  background: var(--primary-color);
}


/*---------------------------------------
  SITE FOOTER              
-----------------------------------------*/
.site-footer {
  background-image: url('../images/templatemo-wave-footer.jpg'), linear-gradient(#FFFFFF, #348CD2);
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
  margin-top: -80px;
  padding-top: 100px;
  padding-bottom: 150px;
}

.site-footer > .container {
  position: relative;
  top: 50px;
  padding-top: 100px;
}

.site-footer-title,
.site-footer p {
  color: var(--white-color);
}

.site-footer p strong {
  font-family: var(--title-font-family);
}

.site-footer p a {
  color: var(--white-color);
  border-bottom: 1px solid;
  padding-bottom: 3px;
}

.site-footer p a:hover {
  color: var(--secondary-color);
}

.site-footer-thumb a img {
  display: block;
  width: 120px;
  min-width: 120px;
  height: auto;
}

.site-footer-links {
  margin-bottom: 0;
  padding-left: 0;
}

.site-footer-link-item {
  display: inline-block;
  list-style: none;
  margin-right: 10px;
  margin-left: 10px;
}

.site-footer-link {
  color: var(--white-color);
  font-size: var(--p-font-size);
  line-height: inherit;
}


/*---------------------------------------
  SOCIAL ICON               
-----------------------------------------*/
.social-icon {
  margin: 0;
  padding: 0;
}

.social-icon-item {
  list-style: none;
  display: inline-block;
  vertical-align: top;
}

.social-icon-link {
  background: var(--secondary-color);
  border-radius: var(--border-radius-large);
  color: var(--white-color);
  font-size: var(--p-font-size);
  display: block;
  margin-right: 10px;
  text-align: center;
  width: 40px;
  height: 40px;
  line-height: 40px;
  transition: background 0.2s, color 0.2s;
}

.social-icon-link:hover {
  background: var(--primary-color);
  color: var(--white-color);
}


/*---------------------------------------
  RESPONSIVE STYLES               
-----------------------------------------*/
@media screen and (min-width: 2160px) {
	.hero-section {
	  background-size: 100% 90%;
	  /* T o o p l a t e . c o m   C u s t o m i z e d */
	}
}


@media screen and (min-width: 1600px) {
  .site-footer {
    padding-top: 250px;
  }
}

@media screen and (max-width: 1240px) {
	.hero-section {
	  background-size: 116%;
	  /* T o o p l a t e . c o m   C u s t o m i z e d */
	}
}

@media screen and (max-width: 991px) {
  h1 {
    font-size: 48px;
  }

  h2 {
    font-size: 36px;
  }

  h3 {
    font-size: 32px;
  }

  h4 {
    font-size: 28px;
  }

  h5 {
    font-size: 20px;
  }

  h6 {
    font-size: 18px;
  }
  
  .hero-section {
	  background-size: 160% 66%; 
	  /* T o o p l a t e . c o m   C u s t o m i z e d */
	}

  .section-padding {
    padding-top: 50px;
    padding-bottom: 50px;
  }

  .navbar-nav {
    background-color: var(--primary-color);
    border-radius: var(--border-radius-medium);
    padding: 30px;
  }

  .navbar-nav .nav-link {
    padding: 5px 0;
  }

  .navbar-nav .dropdown-menu {
    position: relative;
    left: 10px;
    opacity: 1;
    pointer-events: auto;
    max-width: 155px;
    margin-top: 10px;
    margin-bottom: 15px;
  }

  .navbar-expand-lg .navbar-nav {
    padding-bottom: 20px;
  }

  .nav-tabs .nav-link:first-child {
    margin-right: 5px;
  }

  .nav-tabs .nav-link {
    font-size: var(--copyright-font-size);
    padding: 10px;
  }

  .copyright-text {
    text-align: center;
  }

  .site-footer {
    margin-top: -200px;
    padding-top: 200px;
    padding-bottom: 100px;
  }
}

@media screen and (max-width: 540px) {
	.hero-section {
  		background-size: 180% 65%;
		/* T o o p l a t e . c o m   C u s t o m i z e d */
	}

  .custom-block .custom-block-top {
    flex-direction: column;
  }

  .custom-block .custom-block-top small:last-child {
    margin-top: 10px;
    margin-bottom: 10px;
  }
}

@media screen and (max-width: 480px) {
  h1 {
    font-size: 36px;
  }

  h2 {
    font-size: 28px;
  }

  h3 {
    font-size: 26px;
  }

  h4 {
    font-size: 22px;
  }

  h5 {
    font-size: 20px;
  }
}

@media screen and (max-width: 414px) {
  .search-form {
    max-width: 200px;
  }

}

